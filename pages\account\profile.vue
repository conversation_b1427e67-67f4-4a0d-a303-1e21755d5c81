<template>
  <div class="bg-white">
    <!-- Main Content -->
    <main class="max-w-[1920px] mx-auto px-[390px] py-40 pb-0">
      <!-- Content Container -->
      <!-- Page Title -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-[#1A1A1A] text-center">Thông tin tài khoản</h1>  
      </div>
      <div class="bg-white border border-[#E9ECEE] rounded-lg p-8 py-40 pt-6 mb-6 max-w-[1140px]" :class="{ 'bg-white': true }">
        <!-- Account Information Grid -->
        <div class="space-y-6">
          <!-- Company Name -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-sm font-medium text-[#333333]">Tên công ty</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.companyName }}</div>
          </div>

          <!-- Tax Code -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-sm font-medium text-[#333333]">Mã số thuế</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.taxCode }}</div>
          </div>

          <!-- Company Address -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-sm font-medium text-[#333333]">Địa chỉ công ty</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.companyAddress }}</div>
          </div>

          <!-- Phone Number -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-sm font-medium text-[#333333]">Số điện thoại</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.phoneNumber }}</div>
          </div>

          <!-- Email -->
          <div class="flex items-center justify-between gap-2">
            <label class="text-sm font-medium text-[#333333]">Email nhận GCN bảo hiểm</label>
            <div class="text-base text-[#333333] font-bold">{{ accountInfo.email }}</div>
          </div>
        </div>
      </div>
    </main>
  </div>
  
</template>

<script setup lang="ts">

useHead({
  title: 'Thông tin tài khoản'
})

const accountInfo = ref({
  companyName: 'Công ty vận tải Hoàng Linh',
  taxCode: '*********',
  companyAddress: '**********',
  phoneNumber: '**********',
  email: '<EMAIL>'
})

</script>

<style scoped>
/* Custom styles to match Figma design exactly */

header {
  height: auto;
}

main {
  flex: 1;
}

footer {
  height: 121.75px;
  position: relative;
}

/* Ensure proper layout spacing */
.max-w-\[1920px\] {
  max-width: 1920px;
}

/* Shadow effects */
header {
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
}
</style>