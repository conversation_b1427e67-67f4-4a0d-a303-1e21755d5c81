import { ref } from 'vue'

/**
 * Composable để quản lý ChangePasswordModal
 * <PERSON><PERSON> thể sử dụng trên nhiều trang khác nhau
 */
export const useChangePasswordModal = () => {
  const showChangePasswordModal = ref(false)

  /**
   * Mở modal đổi mật khẩu
   */
  const openChangePasswordModal = () => {
    showChangePasswordModal.value = true
  }

  /**
   * Đóng modal đổi mật khẩu
   */
  const closeChangePasswordModal = () => {
    showChangePasswordModal.value = false
  }

  /**
   * Xử lý khi đổi mật khẩu thành công
   */
  const onPasswordChangeSuccess = () => {
    showChangePasswordModal.value = false
    // C<PERSON> thể thêm logic khác như hiển thị thông báo thành công
    console.log('Đổi mật khẩu thành công!')
  }

  return {
    showChangePasswordModal,
    openChangePasswordModal,
    closeChangePasswordModal,
    onPasswordChangeSuccess
  }
}